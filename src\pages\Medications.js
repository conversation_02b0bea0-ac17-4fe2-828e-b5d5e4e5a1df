import React, { useState } from 'react';
import { 
  PlusIcon, 
  BeakerIcon, 
  ClockIcon, 
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useHealthStore } from '../store/healthStore';
import toast from 'react-hot-toast';

const Medications = () => {
  const { medications, addMedication, updateMedication } = useHealthStore();
  const [showAddForm, setShowAddForm] = useState(false);
  const [newMedication, setNewMedication] = useState({
    name: '',
    dosage: '',
    frequency: 'daily',
    times: ['08:00'],
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    notes: '',
    active: true
  });

  const handleAddMedication = () => {
    if (!newMedication.name || !newMedication.dosage) {
      toast.error('Please fill in medication name and dosage');
      return;
    }

    const medication = {
      ...newMedication,
      dosesLog: [],
      reminders: true,
      adherenceRate: 100
    };

    addMedication(medication);
    setNewMedication({
      name: '',
      dosage: '',
      frequency: 'daily',
      times: ['08:00'],
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      notes: '',
      active: true
    });
    setShowAddForm(false);
    toast.success('Medication added successfully!');
  };

  const handleTakeMedication = (medicationId) => {
    const medication = medications.find(m => m.id === medicationId);
    const now = new Date();
    const doseLog = {
      timestamp: now,
      taken: true,
      missed: false,
      scheduledTime: now.toTimeString().slice(0, 5)
    };

    const updatedDosesLog = [...(medication.dosesLog || []), doseLog];
    updateMedication(medicationId, { dosesLog: updatedDosesLog });
    toast.success(`${medication.name} marked as taken!`);
  };

  const handleMissedMedication = (medicationId) => {
    const medication = medications.find(m => m.id === medicationId);
    const now = new Date();
    const doseLog = {
      timestamp: now,
      taken: false,
      missed: true,
      scheduledTime: now.toTimeString().slice(0, 5)
    };

    const updatedDosesLog = [...(medication.dosesLog || []), doseLog];
    updateMedication(medicationId, { dosesLog: updatedDosesLog });
    toast.error(`${medication.name} marked as missed`);
  };

  const getAdherenceRate = (medication) => {
    if (!medication.dosesLog || medication.dosesLog.length === 0) return 100;
    const totalDoses = medication.dosesLog.length;
    const takenDoses = medication.dosesLog.filter(log => log.taken).length;
    return Math.round((takenDoses / totalDoses) * 100);
  };

  const getNextDoseTime = (medication) => {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    for (const time of medication.times) {
      const [hours, minutes] = time.split(':').map(Number);
      const doseTime = hours * 60 + minutes;
      
      if (doseTime > currentTime) {
        return time;
      }
    }
    
    // If no more doses today, return first dose of tomorrow
    return medication.times[0] + ' (tomorrow)';
  };

  const activeMedications = medications.filter(m => m.active);
  const inactiveMedications = medications.filter(m => !m.active);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Medications</h1>
          <p className="text-gray-600">Manage your medications and track adherence</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <PlusIcon className="h-5 w-5" />
          <span>Add Medication</span>
        </button>
      </div>

      {/* Add Medication Form */}
      {showAddForm && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Medication</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Medication Name
              </label>
              <input
                type="text"
                value={newMedication.name}
                onChange={(e) => setNewMedication({...newMedication, name: e.target.value})}
                className="input-field"
                placeholder="e.g., Aspirin"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dosage
              </label>
              <input
                type="text"
                value={newMedication.dosage}
                onChange={(e) => setNewMedication({...newMedication, dosage: e.target.value})}
                className="input-field"
                placeholder="e.g., 100mg"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Frequency
              </label>
              <select
                value={newMedication.frequency}
                onChange={(e) => setNewMedication({...newMedication, frequency: e.target.value})}
                className="input-field"
              >
                <option value="daily">Daily</option>
                <option value="twice-daily">Twice Daily</option>
                <option value="three-times">Three Times Daily</option>
                <option value="weekly">Weekly</option>
                <option value="as-needed">As Needed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time(s)
              </label>
              <input
                type="time"
                value={newMedication.times[0]}
                onChange={(e) => setNewMedication({...newMedication, times: [e.target.value]})}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={newMedication.startDate}
                onChange={(e) => setNewMedication({...newMedication, startDate: e.target.value})}
                className="input-field"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date (Optional)
              </label>
              <input
                type="date"
                value={newMedication.endDate}
                onChange={(e) => setNewMedication({...newMedication, endDate: e.target.value})}
                className="input-field"
              />
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={newMedication.notes}
              onChange={(e) => setNewMedication({...newMedication, notes: e.target.value})}
              className="input-field"
              rows="3"
              placeholder="Any special instructions or notes..."
            />
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setShowAddForm(false)}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button
              onClick={handleAddMedication}
              className="btn-primary"
            >
              Add Medication
            </button>
          </div>
        </div>
      )}

      {/* Active Medications */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Active Medications</h3>
        {activeMedications.length === 0 ? (
          <div className="text-center py-8">
            <BeakerIcon className="mx-auto h-12 w-12 text-gray-300" />
            <p className="mt-2 text-gray-500">No active medications</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="mt-2 text-primary-600 hover:text-primary-500"
            >
              Add your first medication
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {activeMedications.map((medication) => {
              const adherenceRate = getAdherenceRate(medication);
              const nextDose = getNextDoseTime(medication);
              
              return (
                <div key={medication.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <BeakerIcon className="h-6 w-6 text-primary-600" />
                        <div>
                          <h4 className="font-medium text-gray-900">{medication.name}</h4>
                          <p className="text-sm text-gray-500">{medication.dosage}</p>
                        </div>
                      </div>
                      
                      <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div>
                          <div className="text-sm text-gray-500">Frequency</div>
                          <div className="font-medium capitalize">{medication.frequency.replace('-', ' ')}</div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Next Dose</div>
                          <div className="font-medium flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            {nextDose}
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-gray-500">Adherence</div>
                          <div className={`font-medium ${adherenceRate >= 80 ? 'text-green-600' : 'text-red-600'}`}>
                            {adherenceRate}%
                          </div>
                        </div>
                      </div>
                      
                      {medication.notes && (
                        <div className="mt-3">
                          <div className="text-sm text-gray-500">Notes</div>
                          <div className="text-sm text-gray-700">{medication.notes}</div>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-2 ml-4">
                      <button
                        onClick={() => handleTakeMedication(medication.id)}
                        className="flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200"
                      >
                        <CheckCircleIcon className="h-4 w-4" />
                        <span className="text-sm">Taken</span>
                      </button>
                      <button
                        onClick={() => handleMissedMedication(medication.id)}
                        className="flex items-center space-x-1 px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200"
                      >
                        <XCircleIcon className="h-4 w-4" />
                        <span className="text-sm">Missed</span>
                      </button>
                    </div>
                  </div>
                  
                  {/* Adherence Progress Bar */}
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-500 mb-1">
                      <span>Adherence Rate</span>
                      <span>{adherenceRate}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          adherenceRate >= 80 ? 'bg-green-500' : adherenceRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${adherenceRate}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Medication History */}
      {inactiveMedications.length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Medication History</h3>
          <div className="space-y-3">
            {inactiveMedications.map((medication) => (
              <div key={medication.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div>
                  <div className="font-medium text-gray-900">{medication.name}</div>
                  <div className="text-sm text-gray-500">{medication.dosage}</div>
                </div>
                <div className="text-sm text-gray-500">
                  Completed
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Medications;