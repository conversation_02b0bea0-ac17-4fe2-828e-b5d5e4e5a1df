import s from"autoprefixer";import e from"cssdb";import o from"@csstools/postcss-progressive-custom-properties";import t from"fs";import i from"path";import r from"browserslist";import n from"postcss-initial";import a from"postcss-pseudo-class-any-link";import c from"css-blank-pseudo";import p from"postcss-page-break";import m from"@csstools/postcss-cascade-layers";import u from"postcss-attribute-case-insensitive";import l from"postcss-clamp";import d from"@csstools/postcss-color-function";import f from"postcss-color-functional-notation";import g from"postcss-custom-media";import b from"postcss-custom-properties";import h from"postcss-custom-selectors";import N from"postcss-dir-pseudo-class";import k from"@csstools/postcss-normalize-display-values";import y from"postcss-double-position-gradients";import v from"postcss-env-function";import w from"postcss-focus-visible";import $ from"postcss-focus-within";import O from"@csstools/postcss-font-format-keywords";import S from"postcss-font-variant";import x from"postcss-gap-properties";import P from"css-has-pseudo";import j from"postcss-color-hex-alpha";import F from"@csstools/postcss-hwb-function";import E from"@csstools/postcss-ic-unit";import C from"postcss-image-set-function";import M from"@csstools/postcss-is-pseudo-class";import _ from"postcss-lab-function";import q from"postcss-logical";import A from"postcss-media-minmax";import R from"@csstools/postcss-nested-calc";import U from"postcss-nesting";import T from"postcss-selector-not";import B from"@csstools/postcss-oklab-function";import I from"postcss-opacity-percentage";import V from"postcss-overflow-shorthand";import W from"postcss-replace-overflow-wrap";import L from"postcss-place";import D from"css-prefers-color-scheme";import H from"postcss-color-rebeccapurple";import J from"@csstools/postcss-stepped-value-functions";import z from"@csstools/postcss-text-decoration-shorthand";import G from"@csstools/postcss-trigonometric-functions";import K from"@csstools/postcss-unset-value";const Q={"blank-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-blank-pseudo/README-BROWSER.md","focus-visible-pseudo-class":"https://github.com/WICG/focus-visible","focus-within-pseudo-class":"https://github.com/jsxtools/focus-within/blob/master/README-BROWSER.md","has-pseudo-class":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-has-pseudo/README-BROWSER.md","prefers-color-scheme-query":"https://github.com/csstools/postcss-plugins/blob/main/plugins/css-prefers-color-scheme/README-BROWSER.md"},X=["blank-pseudo-class","focus-visible-pseudo-class","focus-within-pseudo-class","has-pseudo-class","prefers-color-scheme-query"];async function Y(s,e,o,t){const i=function(s){return`:root {\n${Object.keys(s).reduce(((e,o)=>(e.push(`\t${o}: ${s[o]};`),e)),[]).join("\n")}\n}\n`}(o),r=function(s){return`${Object.keys(s).reduce(((e,o)=>(e.push(`@custom-media ${o} ${s[o]};`),e)),[]).join("\n")}\n`}(e),n=function(s){return`${Object.keys(s).reduce(((e,o)=>(e.push(`@custom-selector ${o} ${s[o]};`),e)),[]).join("\n")}\n`}(t),a=`${r}\n${n}\n${i}`;await ts(s,a)}function Z(s,e){return`\n\t${s}: {\n${Object.keys(e).reduce(((s,o)=>(s.push(`\t\t'${is(o)}': '${is(e[o])}'`),s)),[]).join(",\n")}\n\t}`}function ss(s,e){return`export const ${s} = {\n${Object.keys(e).reduce(((s,o)=>(s.push(`\t'${is(o)}': '${is(e[o])}'`),s)),[]).join(",\n")}\n};\n`}function es(s,e){return Promise.all([].concat(e).map((async e=>{if(e instanceof Function)await e({customMedia:os(s.customMedia),customProperties:os(s.customProperties),customSelectors:os(s.customSelectors)});else{const o=e===Object(e)?e:{to:String(e)},t=o.toJSON||os;if("customMedia"in o||"customProperties"in o||"customSelectors"in o)o.customMedia=t(s.customMedia),o.customProperties=t(s.customProperties),o.customSelectors=t(s.customSelectors);else if("custom-media"in o||"custom-properties"in o||"custom-selectors"in o)o["custom-media"]=t(s.customMedia),o["custom-properties"]=t(s.customProperties),o["custom-selectors"]=t(s.customSelectors);else{const e=String(o.to||""),r=(o.type||i.extname(o.to).slice(1)).toLowerCase(),n=t(s.customMedia),a=t(s.customProperties),c=t(s.customSelectors);"css"===r&&await Y(e,n,a,c),"js"===r&&await async function(s,e,o,t){const i=`module.exports = {${Z("customMedia",e)},${Z("customProperties",o)},${Z("customSelectors",t)}\n};\n`;await ts(s,i)}(e,n,a,c),"json"===r&&await async function(s,e,o,t){const i=`${JSON.stringify({"custom-media":e,"custom-properties":o,"custom-selectors":t},null,"  ")}\n`;await ts(s,i)}(e,n,a,c),"mjs"===r&&await async function(s,e,o,t){const i=`${ss("customMedia",e)}\n${ss("customProperties",o)}\n${ss("customSelectors",t)}`;await ts(s,i)}(e,n,a,c)}}})))}function os(s){return Object.keys(s).reduce(((e,o)=>(e[o]=String(s[o]),e)),{})}function ts(s,e){return new Promise(((o,i)=>{t.writeFile(s,e,(s=>{s?i(s):o()}))}))}function is(s){return s.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r")}function rs(s,e){if(!s)return!1;if("string"==typeof s)return!0;if(Array.isArray(s)){for(let o=0;o<s.length;o++){if("string"==typeof s[o])return!0;if(s[o]&&e in Object(s[o]))return!0}return!1}return e in Object(s)}function ns(s,e,o){return Math.max(s,Math.min(e,o))}const as=Symbol("insertBefore"),cs=Symbol("insertAfter"),ps=Symbol("insertOrder"),ms=Symbol("plugin");function us(s,e,o){if("insertBefore"!==o&&"insertAfter"!==o)return[];const t="insertBefore"===o?as:cs,i=[];for(const o in e){if(!Object.hasOwnProperty.call(e,o))continue;if(!s.find((s=>s.id===o)))continue;let r=e[o];Array.isArray(r)||(r=[r]);for(let s=0;s<r.length;s++)i.push({id:o,[ms]:r[s],[ps]:s,[t]:!0})}return i}var ls=["custom-media-queries","custom-properties","environment-variables","image-set-function","media-query-ranges","prefers-color-scheme-query","nesting-rules","custom-selectors","any-link-pseudo-class","case-insensitive-attributes","focus-visible-pseudo-class","focus-within-pseudo-class","not-pseudo-class","logical-properties-and-values","dir-pseudo-class","all-property","color-functional-notation","double-position-gradients","hexadecimal-alpha-notation","hwb-function","lab-function","rebeccapurple-color","blank-pseudo-class","break-properties","font-variant-property","is-pseudo-class","has-pseudo-class","gap-properties","overflow-property","overflow-wrap-property","place-properties","system-ui-font-family","cascade-layers","stepped-value-functions","trigonometric-functions"];function ds(){return{postcssPlugin:"postcss-system-ui-font",Declaration(s){fs.test(s.prop)&&(s.value.includes(bs.join(", "))||(s.value=s.value.replace(hs,Ns)))}}}ds.postcss=!0;const fs=/(?:^(?:-|\\002d){2})|(?:^font(?:-family)?$)/i,gs="[\\f\\n\\r\\x09\\x20]",bs=["system-ui","-apple-system","Segoe UI","Roboto","Ubuntu","Cantarell","Noto Sans","sans-serif"],hs=new RegExp(`(^|,|${gs}+)(?:system-ui${gs}*)(?:,${gs}*(?:${bs.join("|")})${gs}*)?(,|$)`,"i"),Ns=`$1${bs.join(", ")}$2`,ks=new Map([["all-property",n],["any-link-pseudo-class",a],["blank-pseudo-class",c],["break-properties",p],["cascade-layers",m],["case-insensitive-attributes",u],["clamp",l],["color-function",d],["color-functional-notation",f],["custom-media-queries",g],["custom-properties",b],["custom-selectors",h],["dir-pseudo-class",N],["display-two-values",k],["double-position-gradients",y],["environment-variables",v],["focus-visible-pseudo-class",w],["focus-within-pseudo-class",$],["font-format-keywords",O],["font-variant-property",S],["gap-properties",x],["has-pseudo-class",P],["hexadecimal-alpha-notation",j],["hwb-function",F],["ic-unit",E],["image-set-function",C],["is-pseudo-class",M],["lab-function",_],["logical-properties-and-values",q],["media-query-ranges",A],["nested-calc",R],["nesting-rules",U],["not-pseudo-class",T],["oklab-function",B],["opacity-percentage",I],["overflow-property",V],["overflow-wrap-property",W],["place-properties",L],["prefers-color-scheme-query",D],["rebeccapurple-color",H],["stepped-value-functions",J],["system-ui-font-family",ds],["text-decoration-shorthand",z],["trigonometric-functions",G],["unset-value",K]]);function ys(s,e,o){return s.concat(us(s,e,"insertBefore"),us(s,o,"insertAfter")).filter((s=>function(s){return!!s[as]||!!s[cs]||!!ks.has(s.id)}(s))).sort(((s,e)=>function(s,e){return s.id===e.id?s[as]&&e[as]||s[cs]&&e[cs]?ns(-1,s[ps]-e[ps],1):s[as]||e[cs]?-1:s[cs]||e[as]?1:0:ns(-1,ls.indexOf(s.id)-ls.indexOf(e.id),1)}(s,e)))}const vs=["and_chr","and_ff","and_qq","and_uc","android","baidu","chrome","edge","firefox","ie","ie_mob","ios_saf","kaios","op_mini","op_mob","opera","safari","samsung"];function ws(s){if(!s)return[];if(!("browser_support"in s))return["> 0%"];const e=[];return vs.forEach((o=>{if("op_mini"===o&&void 0===s.browser_support[o])return void e.push("op_mini all");const t=s.browser_support[o];"string"==typeof t&&/^[0-9|.]+$/.test(t)?e.push(`${o} < ${s.browser_support[o]}`):e.push(`${o} >= 1`)})),e}function $s(s,e,o,t){const i=r(s,{ignoreUnknownVersions:!0});switch(e.id){case"is-pseudo-class":return{onComplexSelector:"warning"};case"nesting-rules":if(function(s,e){const o=ws(s);if(e.some((s=>r(o,{ignoreUnknownVersions:!0}).some((e=>e===s)))))return!0;return!1}(o.find((s=>"is-pseudo-class"===s.id)),i))return t.log('Disabling :is on "nesting-rules" due to lack of browser support.'),{noIsPseudoSelector:!0};return{};case"any-link-pseudo-class":if(i.find((s=>s.startsWith("ie ")||s.startsWith("edge "))))return t.log('Adding area[href] fallbacks for ":any-link" support in Edge and IE.'),{subFeatures:{areaHrefNeedsFixing:!0}};return{};default:return{}}}function Os(s,e,o,t){const i=Object(e.features),n=!("enableClientSidePolyfills"in e)||e.enableClientSidePolyfills,a=Object(e.insertBefore),c=Object(e.insertAfter),p=e.browsers,m=ns(0,function(s){const e=parseInt(s,10);return Number.isNaN(e)?0:e}(e.minimumVendorImplementations),3);m>0&&t.log(`Using features with ${m} or more vendor implementations`);const u=function(s,e){let o=2;if(void 0===s.stage)return e.log(`Using features from Stage ${o} (default)`),o;if(!1===s.stage)o=5;else{let e=parseInt(s.stage,10);Number.isNaN(e)&&(e=0),o=ns(0,e,5)}return 5===o?e.log('Stage has been disabled, features will be handled via the "features" option.'):e.log(`Using features from Stage ${o}`),o}(e,t);2===u&&o&&!1===o.preserve&&(s=JSON.parse(JSON.stringify(s))).forEach((s=>{("blank-pseudo-class"===s.id||"prefers-color-scheme-query"===s.id)&&(s.stage=1)}));const l=ys(s,a,c).map((s=>function(s){const e=ws(s);if(s[as]||s[cs]){let o=s.id;return o=s.insertBefore?`before-${o}`:`after-${o}`,{browsers:e,vendors_implementations:s.vendors_implementations,plugin:s[ms],id:o,stage:6}}return{browsers:e,vendors_implementations:s.vendors_implementations,plugin:ks.get(s.id),id:s.id,stage:s.stage}}(s))).filter((s=>0===m||(!(!s[as]&&!s[cs])||(m<=s.vendors_implementations||(i[s.id]?(t.log(`  ${s.id} does not meet the required vendor implementations but has been enabled by options`),!0):(t.log(`  ${s.id} with ${s.vendors_implementations} vendor implementations has been disabled`),!1)))))).filter((s=>{const e=s.stage>=u,o=n||!X.includes(s.id),r=!1===i[s.id],a=i[s.id]?i[s.id]:e&&o;return r?t.log(`  ${s.id} has been disabled by options`):e?o||t.log(`  ${s.id} has been disabled by "enableClientSidePolyfills: false".`):a?t.log(`  ${s.id} does not meet the required stage but has been enabled by options`):t.log(`  ${s.id} with stage ${s.stage} has been disabled`),a})).map((e=>function(s,e,o,t,i,r){let n,a;return n=$s(e,t,s,r),!0===o[t.id]?i&&(n=Object.assign({},n,i)):n=i?Object.assign({},n,i,o[t.id]):Object.assign({},n,o[t.id]),n.enableProgressiveCustomProperties=!1,a=t.plugin.postcss&&"function"==typeof t.plugin?t.plugin(n):t.plugin&&t.plugin.default&&"function"==typeof t.plugin.default&&t.plugin.default.postcss?t.plugin.default(n):t.plugin,{browsers:t.browsers,vendors_implementations:t.vendors_implementations,plugin:a,pluginOptions:n,id:t.id}}(s,p,i,e,o,t))),d=r(p,{ignoreUnknownVersions:!0});return l.filter((s=>{if(s.id in i)return i[s.id];if(function(s){if("importFrom"in Object(s.pluginOptions))switch(s.id){case"custom-media-queries":if(rs(s.pluginOptions.importFrom,"customMedia"))return!0;break;case"custom-properties":if(rs(s.pluginOptions.importFrom,"customProperties"))return!0;break;case"environment-variables":if(rs(s.pluginOptions.importFrom,"environmentVariables"))return!0;break;case"custom-selectors":if(rs(s.pluginOptions.importFrom,"customSelectors"))return!0}if("exportTo"in Object(s.pluginOptions))switch(s.id){case"custom-media-queries":if(rs(s.pluginOptions.exportTo,"customMedia"))return!0;break;case"custom-properties":if(rs(s.pluginOptions.exportTo,"customProperties"))return!0;break;case"environment-variables":if(rs(s.pluginOptions.exportTo,"environmentVariables"))return!0;break;case"custom-selectors":if(rs(s.pluginOptions.exportTo,"customSelectors"))return!0}return!1}(s))return!0;const e=r(s.browsers,{ignoreUnknownVersions:!0}),o=d.some((s=>e.some((e=>e===s))));return o||t.log(`${s.id} disabled due to browser support`),o}))}class Ss{constructor(){this.logs=[]}log(s){this.logs.push(s)}resetLogger(){this.logs.length=0}dumpLogs(s){s&&this.logs.forEach((e=>s.warn(e))),this.resetLogger()}}var xs=[{packageName:"css-blank-pseudo",id:"blank-pseudo-class",importName:"postcssBlankPseudo"},{packageName:"css-has-pseudo",id:"has-pseudo-class",importName:"postcssHasPseudo"},{packageName:"css-prefers-color-scheme",id:"prefers-color-scheme-query",importName:"postcssPrefersColorScheme"},{packageName:"postcss-attribute-case-insensitive",id:"case-insensitive-attributes",importName:"postcssAttributeCaseInsensitive"},{packageName:"postcss-clamp",id:"clamp",importName:"postcssClamp"},{packageName:"@csstools/postcss-color-function",id:"color-function",importName:"postcssColorFunction"},{packageName:"postcss-color-functional-notation",id:"color-functional-notation",importName:"postcssColorFunctionalNotation"},{packageName:"postcss-color-hex-alpha",id:"hexadecimal-alpha-notation",importName:"postcssColorHexAlpha"},{packageName:"postcss-color-rebeccapurple",id:"rebeccapurple-color",importName:"postcssColorRebeccapurple"},{packageName:"postcss-custom-media",id:"custom-media-queries",importName:"postcssCustomMedia"},{packageName:"postcss-custom-properties",id:"custom-properties",importName:"postcssCustomProperties"},{packageName:"postcss-custom-selectors",id:"custom-selectors",importName:"postcssCustomSelectors"},{packageName:"postcss-dir-pseudo-class",id:"dir-pseudo-class",importName:"postcssDirPseudoClass"},{packageName:"postcss-double-position-gradients",id:"double-position-gradients",importName:"postcssDoublePositionGradients"},{packageName:"postcss-env-function",id:"environment-variables",importName:"postcssEnvFunction"},{packageName:"postcss-focus-visible",id:"focus-visible-pseudo-class",importName:"postcssFocusVisible"},{packageName:"postcss-focus-within",id:"focus-within-pseudo-class",importName:"postcssFocusWithin"},{packageName:"@csstools/postcss-font-format-keywords",id:"font-format-keywords",importName:"postcssFontFormatKeywords"},{packageName:"postcss-font-variant",id:"font-variant-property",importName:"postcssFontVariant"},{packageName:"postcss-gap-properties",id:"gap-properties",importName:"postcssGapProperties"},{packageName:"@csstools/postcss-hwb-function",id:"hwb-function",importName:"postcssHWBFunction"},{packageName:"@csstools/postcss-ic-unit",id:"ic-unit",importName:"postcssICUnit"},{packageName:"postcss-image-set-function",id:"image-set-function",importName:"postcssImageSetFunction"},{packageName:"postcss-initial",id:"all-property",importName:"postcssInitial"},{packageName:"@csstools/postcss-is-pseudo-class",id:"is-pseudo-class",importName:"postcssIsPseudoClass"},{packageName:"postcss-lab-function",id:"lab-function",importName:"postcssLabFunction"},{packageName:"postcss-logical",id:"logical-properties-and-values",importName:"postcssLogical"},{packageName:"postcss-media-minmax",id:"media-query-ranges",importName:"postcssMediaMinmax"},{packageName:"postcss-nesting",id:"nesting-rules",importName:"postcssNesting"},{packageName:"@csstools/postcss-normalize-display-values",id:"display-two-values",importName:"postcssNormalizeDisplayValues"},{packageName:"@csstools/postcss-oklab-function",id:"oklab-function",importName:"postcssOKLabFunction"},{packageName:"postcss-opacity-percentage",id:"opacity-percentage",importName:"postcssOpacityPercentage"},{packageName:"postcss-overflow-shorthand",id:"overflow-property",importName:"postcssOverflowShorthand"},{packageName:"postcss-page-break",id:"break-properties",importName:"postcssPageBreak"},{packageName:"postcss-place",id:"place-properties",importName:"postcssPlace"},{packageName:"postcss-pseudo-class-any-link",id:"any-link-pseudo-class",importName:"postcssPseudoClassAnyLink"},{packageName:"postcss-replace-overflow-wrap",id:"overflow-wrap-property",importName:"postcssReplaceOverflowWrap"},{packageName:"postcss-selector-not",id:"not-pseudo-class",importName:"postcssSelectorNot"},{packageName:"@csstools/postcss-stepped-value-functions",id:"stepped-value-functions",importName:"postcssSteppedValueFunctions"},{packageName:"postcss-system-ui-font-family",importedPackage:"../patch/postcss-system-ui-font-family.mjs",id:"system-ui-font-family",importName:"postcssFontFamilySystemUI"},{packageName:"@csstools/postcss-unset-value",id:"unset-value",importName:"postcssUnsetValue"},{packageName:"@csstools/postcss-cascade-layers",id:"cascade-layers",importName:"postcssCascadeLayers"},{packageName:"@csstools/postcss-trigonometric-functions",id:"trigonometric-functions",importName:"postcssTrigonometricFunctions"},{packageName:"@csstools/postcss-nested-calc",id:"nested-calc",importName:"postcssNestedCalc"},{packageName:"@csstools/postcss-text-decoration-shorthand",id:"text-decoration-shorthand",importName:"postcssTextDecorationShorthand"}];function Ps(s,e,o){const t=xs.map((s=>s.id)),i=xs.map((s=>s.packageName)),r=function(){const s={};return xs.forEach((e=>{s[e.packageName]=e.id})),s}();s.forEach((s=>{if(t.includes(s))return;const n=js(s,t),a=js(s,i);Math.min(n.distance,a.distance)>10?e.warn(o`Unknown feature: "${s}", see the list of features https://github.com/csstools/postcss-plugins/blob/main/plugin-packs/postcss-preset-env/FEATURES.md`):n.distance<a.distance?e.warn(o,`Unknown feature: "${s}", did you mean: "${n.mostSimilar}"`):e.warn(o,`Unknown feature: "${s}", did you mean: "${r[a.mostSimilar]}"`)}))}function js(s,e){let o="unknown",t=1/0;for(let i=0;i<e.length;i++){const r=Fs(s,e[i]);r<t&&(t=r,o=e[i])}return{mostSimilar:o,distance:t}}function Fs(s,e){if(!s.length)return e.length;if(!e.length)return s.length;const o=[];for(let t=0;t<=e.length;t++){o[t]=[t];for(let i=1;i<=s.length;i++)o[t][i]=0===t?i:Math.min(o[t-1][i]+1,o[t][i-1]+1,o[t-1][i-1]+(s[i-1]===e[t-1]?0:1))}return o[e.length][s.length]}const Es=t=>{const i=new Ss,r=Object(t),n=Object.keys(Object(r.features)),a=r.browsers,c=function(s){if("importFrom"in s||"exportTo"in s||"preserve"in s){const e={};return"importFrom"in s&&(e.importFrom=s.importFrom),"exportTo"in s&&(e.exportTo={customMedia:{},customProperties:{},customSelectors:{}}),"preserve"in s&&(e.preserve=s.preserve),e}return!1}(r),p=Os(e,r,c,i),m=p.map((s=>s.plugin));!1!==r.autoprefixer&&m.push(s(Object.assign({overrideBrowserslist:a},r.autoprefixer))),m.push(o()),function(s,e,o){if(e.debug){o.log("Enabling the following feature(s):");const e=[];s.forEach((s=>{s.id.startsWith("before")||s.id.startsWith("after")?o.log(`  ${s.id} (injected via options)`):o.log(`  ${s.id}`),void 0!==Q[s.id]&&e.push(s.id)})),e.length&&(o.log("These feature(s) need a browser library to work:"),e.forEach((s=>o.log(` ${s}: ${Q[s]}`))))}}(p,r,i);const u=()=>({postcssPlugin:"postcss-preset-env",OnceExit:function(s,{result:e}){Ps(n,s,e),r.debug&&i.dumpLogs(e),i.resetLogger(),r.exportTo&&es(c.exportTo,t.exportTo)}});return u.postcss=!0,{postcssPlugin:"postcss-preset-env",plugins:[...m,u()]}};Es.postcss=!0;export{Es as default};
