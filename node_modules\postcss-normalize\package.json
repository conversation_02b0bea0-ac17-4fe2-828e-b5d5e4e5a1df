{"name": "postcss-normalize", "version": "10.0.1", "description": "Use the parts of normalize.css or sanitize.css you need from your browserslist", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "repository": "csstools/postcss-normalize", "homepage": "https://github.com/csstools/postcss-normalize#readme", "bugs": "https://github.com/csstools/postcss-normalize/issues", "main": "./index.cjs", "module": "./index.mjs", "types": "./index.d.ts", "exports": {"require": "./index.cjs", "import": "./index.mjs", "default": "./index.mjs"}, "files": ["index.d.ts", "index.cjs", "index.mjs"], "scripts": {"prepublishOnly": "npm test", "build": "rollup --config .rollup.js --silent", "lint": "package-check && eslint --cache .gitignore --quiet", "test": "npm run lint && npm run build && npm run tape", "tape": "postcss-tape"}, "engines": {"node": ">= 12"}, "peerDependencies": {"browserslist": ">= 4", "postcss": ">= 8"}, "dependencies": {"@csstools/normalize.css": "*", "postcss-browser-comments": "^4", "sanitize.css": "*"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.15.6", "@skypack/package-check": "^0.2.2", "browserslist": "^4.16.5", "eslint": "^7.32.0", "postcss": "^8.2.13", "postcss-import": "^14.0.2", "postcss-tape": "^6.0.1", "pre-commit": "^1.2.2", "rollup": "^2.56.3", "rollup-plugin-babel": "^4.4.0"}, "babel": {"plugins": ["@babel/plugin-syntax-import-meta"], "presets": [["@babel/env", {"targets": "maintained node versions"}]]}, "eslintConfig": {"extends": "eslint:recommended", "ignorePatterns": [".tape.js"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "ecmaFeatures": {"modules": true}}, "rules": {"semi": ["error", "never"]}}, "keywords": ["postcss", "css", "postcss-plugin", "normalizes", "browsers", "fixes"]}