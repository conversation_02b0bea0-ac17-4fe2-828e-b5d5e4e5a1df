{"name": "postcss-normalize-timing-functions", "version": "5.1.0", "description": "Normalize CSS animation/transition timing functions.", "main": "src/index.js", "types": "types/index.d.ts", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "homepage": "https://github.com/cssnano/cssnano", "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-timing-functions\n\n> Normalize timing functions with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-timing-functions) do:\n\n```\nnpm install postcss-normalize-timing-functions --save\n```\n\n## Example\n\n### Input\n\n```css\ndiv {\n    animate: fade 3s cubic-bezier(0.42, 0, 1, 1)\n}\n```\n\n### Output\n\n```css\ndiv {\n    animate: fade 3s ease-in\n}\n``` \n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [Ben Briggs](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}