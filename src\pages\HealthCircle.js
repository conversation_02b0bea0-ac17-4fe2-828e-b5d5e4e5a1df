import React, { useState } from 'react';
import { 
  UsersIcon, 
  PlusIcon,
  HeartIcon,
  FireIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { useHealthStore } from '../store/healthStore';
import toast from 'react-hot-toast';

const HealthCircle = () => {
  const { } = useHealthStore();
  const [showCreateCircle, setShowCreateCircle] = useState(false);
  const [showJoinCircle, setShowJoinCircle] = useState(false);
  const [circleCode, setCircleCode] = useState('');
  const [newCircleName, setNewCircleName] = useState('');
  const [selectedTab, setSelectedTab] = useState('overview');

  // Mock data for demonstration
  const [myCircles] = useState([
    {
      id: 1,
      name: 'Family Health Circle',
      members: [
        { id: 1, name: 'You', avatar: '👤', streak: 15, points: 1250, status: 'active' },
        { id: 2, name: '<PERSON> (<PERSON>)', avatar: '👩', streak: 8, points: 890, status: 'active' },
        { id: 3, name: '<PERSON> (<PERSON>)', avatar: '👨', streak: 12, points: 1100, status: 'active' },
        { id: 4, name: 'Emma (Sister)', avatar: '👧', streak: 5, points: 650, status: 'inactive' }
      ],
      challenges: [
        { id: 1, name: '10,000 Steps Daily', progress: 75, participants: 4, endDate: '2024-02-15' },
        { id: 2, name: 'Medication Adherence', progress: 92, participants: 3, endDate: '2024-02-20' }
      ],
      recentActivity: [
        { user: 'Sarah', action: 'completed', item: 'Blood pressure reading', time: '2 hours ago' },
        { user: 'Mike', action: 'achieved', item: '7-day medication streak', time: '4 hours ago' },
        { user: 'You', action: 'logged', item: 'Mood: 8/10', time: '6 hours ago' }
      ]
    }
  ]);

  const [leaderboard] = useState([
    { rank: 1, name: 'You', points: 1250, streak: 15, badge: '🏆' },
    { rank: 2, name: 'Mike (Dad)', points: 1100, streak: 12, badge: '🥈' },
    { rank: 3, name: 'Sarah (Mom)', points: 890, streak: 8, badge: '🥉' },
    { rank: 4, name: 'Emma (Sister)', points: 650, streak: 5, badge: '⭐' }
  ]);

  const createHealthCircle = () => {
    if (!newCircleName.trim()) {
      toast.error('Please enter a circle name');
      return;
    }
    
    // In real app, this would create a circle on the backend
    const circleCode = Math.random().toString(36).substring(2, 8).toUpperCase();
    toast.success(`Health Circle "${newCircleName}" created! Share code: ${circleCode}`);
    setShowCreateCircle(false);
    setNewCircleName('');
  };

  const joinHealthCircle = () => {
    if (!circleCode.trim()) {
      toast.error('Please enter a circle code');
      return;
    }
    
    // In real app, this would join an existing circle
    toast.success(`Joined health circle with code: ${circleCode}`);
    setShowJoinCircle(false);
    setCircleCode('');
  };

  const shareProgress = (achievement) => {
    const shareText = `🎉 I just ${achievement} in my Health Circle! Join me on MediMate X to track your wellness journey together.`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Health Achievement',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success('Achievement copied to clipboard!');
    }
  };

  const encourageMember = (memberName) => {
    toast.success(`Encouragement sent to ${memberName}! 💪`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Health Circle</h1>
          <p className="text-gray-600">Connect with family and friends for better health together</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowCreateCircle(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Create Circle</span>
          </button>
          <button
            onClick={() => setShowJoinCircle(true)}
            className="btn-secondary flex items-center space-x-2"
          >
            <UsersIcon className="h-5 w-5" />
            <span>Join Circle</span>
          </button>
        </div>
      </div>

      {/* Create Circle Modal */}
      {showCreateCircle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Create Health Circle</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Circle Name
                </label>
                <input
                  type="text"
                  value={newCircleName}
                  onChange={(e) => setNewCircleName(e.target.value)}
                  className="input-field"
                  placeholder="e.g., Family Health Circle"
                />
              </div>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-700">
                  You'll receive a unique code to share with family and friends so they can join your circle.
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreateCircle(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={createHealthCircle}
                className="btn-primary"
              >
                Create Circle
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Join Circle Modal */}
      {showJoinCircle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Join Health Circle</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Circle Code
                </label>
                <input
                  type="text"
                  value={circleCode}
                  onChange={(e) => setCircleCode(e.target.value.toUpperCase())}
                  className="input-field"
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                />
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <p className="text-sm text-green-700">
                  Ask a family member or friend for their Health Circle code to join their group.
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowJoinCircle(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={joinHealthCircle}
                className="btn-primary"
              >
                Join Circle
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {['overview', 'leaderboard', 'challenges', 'activity'].map((tab) => (
            <button
              key={tab}
              onClick={() => setSelectedTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                selectedTab === tab
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Overview Tab */}
      {selectedTab === 'overview' && (
        <div className="space-y-6">
          {myCircles.length === 0 ? (
            <div className="text-center py-12">
              <UsersIcon className="mx-auto h-12 w-12 text-gray-300" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Health Circles</h3>
              <p className="mt-1 text-sm text-gray-500">
                Create or join a health circle to start your wellness journey with others.
              </p>
            </div>
          ) : (
            myCircles.map((circle) => (
              <div key={circle.id} className="card">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{circle.name}</h3>
                  <div className="flex items-center space-x-2">
                    <UsersIcon className="h-5 w-5 text-gray-400" />
                    <span className="text-sm text-gray-500">{circle.members.length} members</span>
                  </div>
                </div>

                {/* Members */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {circle.members.map((member) => (
                    <div key={member.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="text-2xl">{member.avatar}</div>
                        <div>
                          <div className="font-medium text-gray-900">{member.name}</div>
                          <div className={`text-xs ${member.status === 'active' ? 'text-green-600' : 'text-gray-500'}`}>
                            {member.status}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <div>
                          <div className="text-gray-500">Streak</div>
                          <div className="font-medium">{member.streak} days</div>
                        </div>
                        <div>
                          <div className="text-gray-500">Points</div>
                          <div className="font-medium">{member.points}</div>
                        </div>
                      </div>
                      {member.name !== 'You' && (
                        <button
                          onClick={() => encourageMember(member.name)}
                          className="w-full mt-2 text-xs bg-blue-100 text-blue-700 py-1 rounded hover:bg-blue-200"
                        >
                          Encourage 💪
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                {/* Active Challenges */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Active Challenges</h4>
                  <div className="space-y-3">
                    {circle.challenges.map((challenge) => (
                      <div key={challenge.id} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <div className="font-medium text-gray-900">{challenge.name}</div>
                          <div className="text-sm text-gray-500">
                            Ends {new Date(challenge.endDate).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>{challenge.participants} participants</span>
                          <span className="font-medium">{challenge.progress}% complete</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${challenge.progress}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Leaderboard Tab */}
      {selectedTab === 'leaderboard' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">This Week's Leaderboard</h3>
          <div className="space-y-3">
            {leaderboard.map((member) => (
              <div key={member.rank} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{member.badge}</div>
                  <div>
                    <div className="font-medium text-gray-900">#{member.rank} {member.name}</div>
                    <div className="text-sm text-gray-500">{member.streak} day streak</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-bold text-primary-600">{member.points}</div>
                  <div className="text-sm text-gray-500">points</div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">How to Earn Points</h4>
            <div className="text-sm text-blue-700 space-y-1">
              <div>• Log vitals: 10 points</div>
              <div>• Take medication on time: 15 points</div>
              <div>• Complete daily goals: 25 points</div>
              <div>• Maintain streak: 5 points/day</div>
              <div>• Encourage others: 5 points</div>
            </div>
          </div>
        </div>
      )}

      {/* Challenges Tab */}
      {selectedTab === 'challenges' && (
        <div className="space-y-6">
          <div className="card">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Available Challenges</h3>
              <button className="btn-primary">Create Challenge</button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <FireIcon className="h-5 w-5 text-orange-500" />
                  <h4 className="font-medium text-gray-900">30-Day Consistency</h4>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Log at least one vital sign every day for 30 days
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">2 participants</span>
                  <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    Join Challenge
                  </button>
                </div>
              </div>
              
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <HeartIcon className="h-5 w-5 text-red-500" />
                  <h4 className="font-medium text-gray-900">Medication Masters</h4>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Achieve 95% medication adherence for 2 weeks
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">3 participants</span>
                  <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                    Join Challenge
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Activity Tab */}
      {selectedTab === 'activity' && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {myCircles[0]?.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 pb-3 border-b border-gray-100 last:border-b-0">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <HeartIcon className="h-4 w-4 text-primary-600" />
                </div>
                <div className="flex-1">
                  <div className="text-sm">
                    <span className="font-medium text-gray-900">{activity.user}</span>
                    <span className="text-gray-600"> {activity.action} </span>
                    <span className="font-medium text-gray-900">{activity.item}</span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{activity.time}</div>
                </div>
                <button
                  onClick={() => shareProgress(activity.item)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <ShareIcon className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default HealthCircle;