import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useHealthStore = create(
  persist(
    (set, get) => ({
      // User Profile
      user: {
        name: '',
        age: '',
        gender: '',
        emergencyContacts: [],
        language: 'en',
        dnaProfile: null,
      },
      
      // Health Data
      vitals: [],
      medications: [],
      symptoms: [],
      moodLogs: [],
      
      // AI & Behavioral Data
      aiMemory: [],
      behaviorPatterns: {},
      chatHistory: [],
      
      // Family & Social
      familyMembers: [],
      healthCircle: [],
      currentProfile: 'self', // 'self' or family member id
      
      // System States
      isRecoveryMode: false,
      emergencyMode: false,
      voiceEnabled: false,
      
      // Actions
      setUser: (userData) => set({ user: { ...get().user, ...userData } }),
      
      addVital: (vital) => set((state) => ({
        vitals: [...state.vitals, { ...vital, id: Date.now(), timestamp: new Date() }]
      })),
      
      addMedication: (medication) => set((state) => ({
        medications: [...state.medications, { ...medication, id: Date.now(), createdAt: new Date() }]
      })),
      
      updateMedication: (id, updates) => set((state) => ({
        medications: state.medications.map(med => 
          med.id === id ? { ...med, ...updates } : med
        )
      })),
      
      addChatMessage: (message) => set((state) => ({
        chatHistory: [...state.chatHistory, { ...message, id: Date.now(), timestamp: new Date() }]
      })),
      
      addAIMemory: (memory) => set((state) => ({
        aiMemory: [...state.aiMemory, { ...memory, timestamp: new Date() }]
      })),
      
      toggleRecoveryMode: () => set((state) => ({ isRecoveryMode: !state.isRecoveryMode })),
      
      setEmergencyMode: (active) => set({ emergencyMode: active }),
      
      addFamilyMember: (member) => set((state) => ({
        familyMembers: [...state.familyMembers, { ...member, id: Date.now() }]
      })),
      
      switchProfile: (profileId) => set({ currentProfile: profileId }),
      
      // Health Analysis
      getVitalsTrends: () => {
        const { vitals } = get();
        const last30Days = vitals.filter(v => 
          new Date(v.timestamp) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        );
        
        return {
          bloodPressure: last30Days.filter(v => v.type === 'bloodPressure'),
          bloodSugar: last30Days.filter(v => v.type === 'bloodSugar'),
          weight: last30Days.filter(v => v.type === 'weight'),
          mood: last30Days.filter(v => v.type === 'mood'),
        };
      },
      
      getMedicationAdherence: () => {
        const { medications } = get();
        const activemedications = medications.filter(m => m.active);
        
        return activemedications.map(med => {
          const totalDoses = med.dosesLog?.length || 0;
          const missedDoses = med.dosesLog?.filter(d => d.missed).length || 0;
          const adherenceRate = totalDoses > 0 ? ((totalDoses - missedDoses) / totalDoses) * 100 : 0;
          
          return {
            ...med,
            adherenceRate,
            totalDoses,
            missedDoses
          };
        });
      },
      
      // Risk Assessment
      assessHealthRisks: () => {
        const { vitals } = get();
        const risks = [];
        
        // Check recent vitals for concerning trends
        const recentVitals = vitals.filter(v => 
          new Date(v.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        );
        
        const bpReadings = recentVitals.filter(v => v.type === 'bloodPressure');
        if (bpReadings.some(bp => bp.systolic > 140 || bp.diastolic > 90)) {
          risks.push({
            type: 'high_blood_pressure',
            severity: 'medium',
            message: 'Recent blood pressure readings are elevated'
          });
        }
        
        // Check medication adherence
        const adherence = get().getMedicationAdherence();
        const poorAdherence = adherence.filter(med => med.adherenceRate < 80);
        if (poorAdherence.length > 0) {
          risks.push({
            type: 'medication_adherence',
            severity: 'medium',
            message: `Poor adherence to ${poorAdherence.length} medication(s)`
          });
        }
        
        return risks;
      }
    }),
    {
      name: 'medimate-health-store',
      partialize: (state) => ({
        user: state.user,
        vitals: state.vitals,
        medications: state.medications,
        familyMembers: state.familyMembers,
        aiMemory: state.aiMemory,
        behaviorPatterns: state.behaviorPatterns,
      }),
    }
  )
);