# Changes to PostCSS RebeccaPurple

### 7.1.1 (July 8, 2022)

- Fix case insensitive matching.

### 7.1.0 (June 10, 2022)

- Added: Typescript typings

### 7.0.2 (January 2, 2022)

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).

### 7.0.1 (December 16, 2021)

- Changed: now uses `postcss-value-parser` for parsing.
- Updated: documentation

### 7.0.0 (September 17, 2021)

- Updated: Support for PostCSS 8+ (major).
- Updated: Support for Node v12+ (major).

### 6.0.0 (April 25, 2020)

- Updated: `postcss-values-parser` to 3.2.0 (major)

## 5.0.0 (January 29, 2020)

- Updated: Support for Node v10+
- Updated: PostCSS Values Parser v3+

## 4.0.1 (September 18, 2018)

- Updated: PostCSS Values Parser v2+

## 4.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+

## 3.1.0 (May 1, 2018)

- Improve `rebeccapurple` pre-parse word detection
- Switched from `postcss-value-parser` to `postcss-values-parser`
- Bump `postcss` from `^6.0.1` to `^6.0.22`

## 3.0.0 (May 1, 2017)

- Added: compatibility with postcss v6.x

## 2.0.1 (November 28, 2016)

- Bump `color` dependency version
([postcss-cssnext/#327](https://github.com/MoOx/postcss-cssnext/issues/327) - @wtgtybhertgeghgtwtg).

## 2.0.0 (September 8, 2015)

- Added: compatibility with postcss v5.x
- Removed: compatiblity with postcss v4.x

## 1.2.0 (August 13, 2015)

- Added: compatibility with postcss v4.1.x
([#4](https://github.com/postcss/postcss-color-rebeccapurple/pull/4))

## 1.1.0 (November 25, 2014)

- Enhanced exceptions

## 1.0.0 (November 4, 2014)

Initial release from [postcss-color](https://github.com/postcss/postcss-color)
