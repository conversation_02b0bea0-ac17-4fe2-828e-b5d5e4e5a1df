import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import LandingPage from './pages/LandingPage';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Dashboard from './pages/Dashboard';
import AIChat from './pages/AIChat';
import Medications from './pages/Medications';
import Vitals from './pages/Vitals';
import Emergency from './pages/Emergency';
import Reports from './pages/Reports';
import Family from './pages/Family';
import Profile from './pages/Profile';
import DNAProfile from './pages/DNAProfile';
import HealthCircle from './pages/HealthCircle';
import AdvancedSettings from './pages/AdvancedSettings';
import { useHealthStore } from './store/healthStore';
import { useAuthStore } from './store/authStore';

function App() {
  const { isRecoveryMode } = useHealthStore();
  const { isAuthenticated } = useAuthStore();

  return (
    <div className={`min-h-screen ${isRecoveryMode ? 'recovery-mode' : ''}`}>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<Signup />} />
          
          {/* Protected Routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Layout>
                <Dashboard />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/chat" element={
            <ProtectedRoute>
              <Layout>
                <AIChat />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/medications" element={
            <ProtectedRoute>
              <Layout>
                <Medications />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/vitals" element={
            <ProtectedRoute>
              <Layout>
                <Vitals />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/emergency" element={
            <ProtectedRoute>
              <Layout>
                <Emergency />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/reports" element={
            <ProtectedRoute>
              <Layout>
                <Reports />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/family" element={
            <ProtectedRoute>
              <Layout>
                <Family />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Layout>
                <Profile />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/dna" element={
            <ProtectedRoute>
              <Layout>
                <DNAProfile />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/circle" element={
            <ProtectedRoute>
              <Layout>
                <HealthCircle />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <Layout>
                <AdvancedSettings />
              </Layout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
      <Toaster position="top-right" />
    </div>
  );
}

export default App;