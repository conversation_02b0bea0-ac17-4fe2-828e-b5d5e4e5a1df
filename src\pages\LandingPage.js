import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  HeartIcon,
  SparklesIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  CpuChipIcon,
  ArrowRightIcon,
  PlayIcon,
  BeakerIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  UsersIcon,
  MicrophoneIcon,
  CameraIcon,
  QrCodeIcon,
  GlobeAltIcon,
  BoltIcon,
  ChartBarIcon,
  ClockIcon,
  DevicePhoneMobileIcon
} from '@heroicons/react/24/outline';

const LandingPage = () => {
  // Core Features with detailed descriptions
  const coreFeatures = [
    {
      icon: CpuChipIcon,
      title: 'AI Health Chatbot (Symptom Checker)',
      description: 'Chat with LLaMA-3/OpenRouter AI for natural symptom analysis. Suggests actions: Rest/Home remedy/Doctor consult with emergency detection.',
      color: 'text-blue-500',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      icon: BeakerIcon,
      title: 'Medication Tracker + <PERSON>ll Reminders',
      description: 'Add meds with timing & dosage. Smart reminders (push/SMS). AI adjusts schedule if missed doses with adherence analytics.',
      color: 'text-purple-500',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      icon: HeartIcon,
      title: 'Vitals & Wellness Logger',
      description: 'BP, Sugar, Sleep, Water, Mood, Weight tracking. Auto graph analysis with trend prediction and risk alerts.',
      color: 'text-red-500',
      gradient: 'from-red-500 to-orange-500'
    },
    {
      icon: ExclamationTriangleIcon,
      title: 'Emergency Smart Alert',
      description: 'Triggers alert if vitals are critical. Geo-location shared with emergency contacts. Nearby hospital suggestions.',
      color: 'text-red-600',
      gradient: 'from-red-600 to-red-700'
    },
    {
      icon: DocumentTextIcon,
      title: 'AI Health Report Generator',
      description: 'Summarized monthly/weekly PDF reports. Shareable via QR or download with professional medical formatting.',
      color: 'text-green-500',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      icon: MicrophoneIcon,
      title: 'Voice & Audio Interface',
      description: 'Voice logging of symptoms. Audio-only chatbot for elderly/blind users with multi-language support.',
      color: 'text-indigo-500',
      gradient: 'from-indigo-500 to-purple-500'
    }
  ];

  // Next-Gen Features
  const nextGenFeatures = [
    {
      icon: SparklesIcon,
      title: 'DNA-Based Wellness Profile',
      description: 'Personalized diet, sleep, fitness based on genetics. 23andMe/AncestryDNA integration support.',
      color: 'text-purple-600'
    },
    {
      icon: UserGroupIcon,
      title: 'Health Circle (Social Layer)',
      description: 'Create family/friend circles. Share progress, encourage habit building. Private leaderboards, challenges.',
      color: 'text-blue-600'
    },
    {
      icon: CameraIcon,
      title: 'Face Emotion Scanner',
      description: 'Detect user\'s mood through face. Adjust chatbot tone + recommendations with privacy protection.',
      color: 'text-green-600'
    },
    {
      icon: CpuChipIcon,
      title: 'AI Memory & Behavioral Patterns',
      description: 'AI tracks user behavior over weeks. Smart nudges: "You always skip water on Sundays".',
      color: 'text-yellow-600'
    },
    {
      icon: QrCodeIcon,
      title: 'Lab Report & Prescription Scanner',
      description: 'OCR-based data extraction. AI explains hard medical terms with automatic data integration.',
      color: 'text-indigo-600'
    },
    {
      icon: GlobeAltIcon,
      title: 'Multi-Language AI Support',
      description: 'Chatbot & UI in Urdu, Hindi, Arabic, Spanish, etc. Cultural health considerations included.',
      color: 'text-pink-600'
    }
  ];

  const stats = [
    { number: '15+', label: 'Core Features', icon: BoltIcon },
    { number: '13+', label: 'Languages', icon: GlobeAltIcon },
    { number: '24/7', label: 'AI Support', icon: ClockIcon },
    { number: '100%', label: 'Privacy First', icon: ShieldCheckIcon }
  ];

  const technicalSpecs = [
    {
      category: 'AI & Machine Learning',
      items: [
        'LLaMA-3/OpenRouter AI Integration',
        'Natural Language Processing',
        'Behavioral Pattern Recognition',
        'Predictive Health Analytics',
        'Face Emotion Detection',
        'Voice Recognition & Synthesis'
      ]
    },
    {
      category: 'Health Features',
      items: [
        'Comprehensive Vitals Tracking',
        'Smart Medication Management',
        'Emergency Response System',
        'DNA-Based Recommendations',
        'Lab Report OCR Scanning',
        'Family Health Management'
      ]
    },
    {
      category: 'Technology Stack',
      items: [
        'React.js 18 with Modern Hooks',
        'Tailwind CSS with Custom Themes',
        'Zustand State Management',
        'Chart.js Health Analytics',
        'Web Speech API Integration',
        'Progressive Web App Ready'
      ]
    },
    {
      category: 'Security & Privacy',
      items: [
        'End-to-End Encryption',
        'Local Data Processing',
        'HIPAA Compliance Ready',
        'No Server Dependencies',
        'Biometric Authentication',
        'Complete Data Portability'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="relative z-10 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <HeartIcon className="h-6 w-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              MediMate X
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-4"
          >
            <Link
              to="/login"
              className="text-gray-600 hover:text-gray-900 font-medium transition-colors"
            >
              Sign In
            </Link>
            <Link
              to="/signup"
              className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105"
            >
              Get Started
            </Link>
          </motion.div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative px-6 py-20">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center space-x-2 bg-blue-100 text-blue-700 px-6 py-3 rounded-full text-lg font-medium"
              >
                <SparklesIcon className="h-6 w-6" />
                <span>World's First AI-Powered Health Ecosystem</span>
              </motion.div>

              <h1 className="text-6xl lg:text-8xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
                  MediMate X
                </span>
                <br />
                <span className="bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                  AI Health Companion
                </span>
              </h1>

              <p className="text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
                Your intelligent health companion for body, mind, and everyday wellness.
                Integrating medical care, behavioral insights, emotional support, and personalized AI recommendations.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/signup"
                className="group bg-gradient-to-r from-blue-500 to-purple-600 text-white px-10 py-5 rounded-2xl font-semibold text-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3"
              >
                <span>Start Your Health Journey</span>
                <ArrowRightIcon className="h-6 w-6 group-hover:translate-x-1 transition-transform" />
              </Link>

              <button className="group bg-white text-gray-700 px-10 py-5 rounded-2xl font-semibold text-xl border-2 border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300 flex items-center justify-center space-x-3">
                <PlayIcon className="h-6 w-6 text-blue-500" />
                <span>Watch Demo</span>
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 pt-12">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="flex items-center justify-center mb-2">
                    <stat.icon className="h-8 w-8 text-blue-600 mr-2" />
                    <div className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      {stat.number}
                    </div>
                  </div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="px-6 py-20 bg-white/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center space-y-4 mb-16"
          >
            <h2 className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 to-blue-900 bg-clip-text text-transparent">
              🚀 Core Modules
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Six powerful core features that transform your health management experience
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {coreFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="bg-white/80 backdrop-blur-sm p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100"
              >
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center mb-6`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Next-Gen Features Section */}
      <section className="px-6 py-20">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center space-y-4 mb-16"
          >
            <h2 className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 to-purple-900 bg-clip-text text-transparent">
              🌌 Next-Gen Features
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Revolutionary features unique to MediMate X that set us apart from any other health platform
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {nextGenFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="bg-gradient-to-br from-white to-gray-50 p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100"
              >
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-gray-100 to-gray-200 flex items-center justify-center mb-6">
                  <feature.icon className={`h-8 w-8 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="px-6 py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center space-y-4 mb-16"
          >
            <h2 className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              🏗️ Technical Excellence
            </h2>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto">
              Built with cutting-edge technology stack for maximum performance, security, and scalability
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {technicalSpecs.map((spec, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-800/50 backdrop-blur-sm p-8 rounded-3xl border border-gray-700"
              >
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mr-3"></div>
                  {spec.category}
                </h3>
                <ul className="space-y-3">
                  {spec.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start space-x-3 text-gray-300">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-6 py-20 bg-gradient-to-r from-blue-600 to-purple-700">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-5xl lg:text-6xl font-bold text-white">
              Ready to Transform Your Health?
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Join the future of healthcare with MediMate X - your AI-powered personal health ecosystem
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/signup"
                className="group bg-white text-blue-600 px-10 py-5 rounded-2xl font-semibold text-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-3"
              >
                <span>Get Started Free</span>
                <ArrowRightIcon className="h-6 w-6 group-hover:translate-x-1 transition-transform" />
              </Link>

              <Link
                to="/login"
                className="bg-transparent border-2 border-white text-white px-10 py-5 rounded-2xl font-semibold text-xl hover:bg-white hover:text-blue-600 transition-all duration-300"
              >
                Sign In
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-12 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <HeartIcon className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold">MediMate X</span>
          </div>
          <p className="text-gray-400 mb-4">
            Your intelligent health companion for body, mind, and everyday wellness.
          </p>
          <p className="text-gray-500 text-sm">
            © 2024 MediMate X. All rights reserved. Built with ❤️ for better health outcomes worldwide.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;