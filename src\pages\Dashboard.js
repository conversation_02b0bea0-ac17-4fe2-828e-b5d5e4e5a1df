import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  HeartIcon, 
  BeakerIcon, 
  ChatBubbleLeftRightIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { useHealthStore } from '../store/healthStore';
import VitalsChart from '../components/Charts/VitalsChart';
import MedicationReminders from '../components/Medications/MedicationReminders';
import HealthRiskAlert from '../components/Health/HealthRiskAlert';
import QuickActions from '../components/Dashboard/QuickActions';
import AIMemoryInsights from '../components/AI/AIMemoryInsights';
import FaceEmotionScanner from '../components/EmotionScanner/FaceEmotionScanner';
import LabReportScanner from '../components/Scanner/LabReportScanner';

const Dashboard = () => {
  const { 
    user, 
    vitals, 
    medications, 
    getVitalsTrends, 
    getMedicationAdherence,
    assessHealthRisks,
    isRecoveryMode 
  } = useHealthStore();
  
  const [healthRisks, setHealthRisks] = useState([]);
  const [vitalsTrends, setVitalsTrends] = useState({});
  const [medicationStats, setMedicationStats] = useState([]);

  useEffect(() => {
    setHealthRisks(assessHealthRisks());
    setVitalsTrends(getVitalsTrends());
    setMedicationStats(getMedicationAdherence());
  }, [vitals, medications, assessHealthRisks, getVitalsTrends, getMedicationAdherence]);

  const getLatestVital = (type) => {
    const typeVitals = vitals.filter(v => v.type === type);
    return typeVitals.length > 0 ? typeVitals[typeVitals.length - 1] : null;
  };

  const latestBP = getLatestVital('bloodPressure');
  const latestWeight = getLatestVital('weight');
  const latestMood = getLatestVital('mood');

  const stats = [
    {
      name: 'Blood Pressure',
      value: latestBP ? `${latestBP.systolic}/${latestBP.diastolic}` : 'No data',
      unit: latestBP ? 'mmHg' : '',
      icon: HeartIcon,
      color: latestBP && (latestBP.systolic > 140 || latestBP.diastolic > 90) ? 'text-red-600' : 'text-green-600',
      trend: 'stable'
    },
    {
      name: 'Weight',
      value: latestWeight ? latestWeight.value : 'No data',
      unit: latestWeight ? 'kg' : '',
      icon: ArrowTrendingUpIcon,
      color: 'text-blue-600',
      trend: 'up'
    },
    {
      name: 'Mood',
      value: latestMood ? latestMood.value : 'No data',
      unit: latestMood ? '/10' : '',
      icon: HeartIcon,
      color: latestMood && latestMood.value >= 7 ? 'text-green-600' : latestMood && latestMood.value >= 4 ? 'text-yellow-600' : 'text-red-600',
      trend: 'stable'
    },
    {
      name: 'Active Medications',
      value: medications.filter(m => m.active).length,
      unit: 'meds',
      icon: BeakerIcon,
      color: 'text-purple-600',
      trend: 'stable'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className={`rounded-lg p-6 ${isRecoveryMode ? 'bg-recovery-bg border border-recovery-accent/20' : 'bg-gradient-to-r from-primary-500 to-primary-600'}`}>
        <div className="flex items-center justify-between">
          <div>
            <h1 className={`text-2xl font-bold ${isRecoveryMode ? 'text-recovery-text' : 'text-white'}`}>
              Welcome back, {user.name || 'User'}!
            </h1>
            <p className={`mt-1 ${isRecoveryMode ? 'text-recovery-text/80' : 'text-primary-100'}`}>
              {isRecoveryMode ? 'Take it easy today. Focus on rest and recovery.' : 'Here\'s your health overview for today'}
            </p>
          </div>
          {isRecoveryMode && (
            <div className="text-recovery-accent">
              <HeartIcon className="h-8 w-8" />
            </div>
          )}
        </div>
      </div>

      {/* Health Risk Alerts */}
      {healthRisks.length > 0 && (
        <div className="space-y-3">
          {healthRisks.map((risk, index) => (
            <HealthRiskAlert key={index} risk={risk} />
          ))}
        </div>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </div>
                    {stat.unit && (
                      <div className="ml-2 text-sm text-gray-500">
                        {stat.unit}
                      </div>
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts and Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Vitals Trends</h3>
          <VitalsChart data={vitalsTrends} />
        </div>
        
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Medication Adherence</h3>
          <div className="space-y-3">
            {medicationStats.slice(0, 3).map((med) => (
              <div key={med.id} className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">{med.name}</div>
                  <div className="text-sm text-gray-500">{med.dosage}</div>
                </div>
                <div className="text-right">
                  <div className={`font-medium ${med.adherenceRate >= 80 ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.round(med.adherenceRate)}%
                  </div>
                  <div className="text-sm text-gray-500">adherence</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Medication Reminders */}
      <MedicationReminders />

      {/* Quick Actions */}
      <QuickActions />

      {/* Advanced Features */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* AI Memory Insights */}
        <AIMemoryInsights />
        
        {/* Face Emotion Scanner */}
        <FaceEmotionScanner />
      </div>

      {/* Recent Activity */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {vitals.slice(-5).reverse().map((vital) => (
            <div key={vital.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
              <div>
                <div className="font-medium text-gray-900 capitalize">{vital.type.replace(/([A-Z])/g, ' $1')}</div>
                <div className="text-sm text-gray-500">
                  {new Date(vital.timestamp).toLocaleDateString()}
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-gray-900">
                  {vital.type === 'bloodPressure' ? `${vital.systolic}/${vital.diastolic}` : vital.value}
                </div>
                <div className="text-sm text-gray-500">
                  {vital.type === 'bloodPressure' ? 'mmHg' : vital.unit}
                </div>
              </div>
            </div>
          ))}
          {vitals.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <HeartIcon className="mx-auto h-12 w-12 text-gray-300" />
              <p className="mt-2">No vitals recorded yet</p>
              <Link to="/vitals" className="text-primary-600 hover:text-primary-500">
                Add your first vital reading
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;