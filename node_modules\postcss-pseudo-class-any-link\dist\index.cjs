"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-selector-parser"));const n=t.default().astSync(":link").nodes[0],s=t.default().astSync(":visited").nodes[0],o=t.default().astSync("area[href]").nodes[0],r=t.default().astSync("[href]").nodes[0];function l(e,l){let c=[];return t.default((e=>{let u=[];e.walkPseudos((e=>{if(":any-link"!==e.value.toLowerCase()||e.nodes&&e.nodes.length)return;if(!l)return void u.push([n.clone(),s.clone()]);const c=function(e){let n=[],s=e.prev();for(;s&&"combinator"!==s.type&&!t.default.isPseudoElement(s);)"tag"===s.type&&n.push(s.value.toLowerCase()),s=s.prev();let o=e.next();for(;o&&"combinator"!==o.type&&!t.default.isPseudoElement(o);)"tag"===o.type&&n.push(o.value.toLowerCase()),o=o.next();return n}(e);c.includes("area")?u.push([n.clone(),s.clone(),r.clone()]):c.length?u.push([n.clone(),s.clone()]):u.push([n.clone(),s.clone(),o.clone()])})),u.length&&function(...e){const t=[],n=e.length-1;function s(o,r){for(let l=0,c=e[r].length;l<c;l++){const c=o.slice(0);c.push(e[r][l]),r==n?t.push(c):s(c,r+1)}}return s([],0),t}(...u).forEach((n=>{const s=e.clone();s.walkPseudos((e=>{":any-link"!==e.value.toLowerCase()||e.nodes&&e.nodes.length||(!function(e,n,s){let o=s.type;"selector"===s.type&&s.nodes&&s.nodes.length&&(o=s.nodes[0].type);let r=-1,l=-1;const c=e.index(n);for(let n=c;n>=0&&("combinator"!==e.nodes[n].type&&!t.default.isPseudoElement(e.nodes[n].type));n--)r=n;if("tag"===o)return void e.insertBefore(e.at(r),s);for(let n=c;n<e.nodes.length&&("combinator"!==e.nodes[n].type&&!t.default.isPseudoElement(e.nodes[n].type));n++)l=n;for(let t=r;t<=l;t++)if(e.nodes[t].type===o)return void e.insertAfter(e.at(t),s);e.insertAfter(e.at(r),s)}(e.parent,e,n.shift()),e.remove())})),c.push(s.toString())}))})).processSync(e),c}function c(e){const t={preserve:!0,...e},n={areaHrefNeedsFixing:!1,...Object(t.subFeatures)};return{postcssPlugin:"postcss-pseudo-class-any-link",Rule(e,{result:s}){if(!e.selector.toLowerCase().includes(":any-link"))return;(e.raws.selector&&e.raws.selector.raw||e.selector).endsWith(":")||function(e,t,n,s){let o=[],r=[];try{for(let t=0;t<e.selectors.length;t++){const n=e.selectors[t],c=l(n,s);c.length?o.push(...c):r.push(n)}}catch(n){return void e.warn(t,`Failed to parse selector : ${e.selector}`)}o.length&&(e.cloneBefore({selectors:o}),r.length&&e.cloneBefore({selectors:r}),n||e.remove())}(e,s,t.preserve,n.areaHrefNeedsFixing)}}}c.postcss=!0,module.exports=c;
