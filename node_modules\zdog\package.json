{"name": "zdog", "version": "1.1.3", "description": "Round, flat, designer-friendly pseudo-3D engine", "main": "js/index.js", "unpkg": "dist/zdog.dist.min.js", "files": ["dist/*.*", "js/*.*", "!js/.*", "!dist/.*"], "devDependencies": {"eslint": "^8.7.0", "eslint-plugin-metafizzy": "^1.0.0", "uglify-js": "^3.6.3"}, "scripts": {"bundle": "node tasks/bundle", "dist": "npm run bundle && npm run uglify", "lint": "npx eslint .", "lintFix": "npx eslint . --fix", "preversion": "npm run lint", "version": "node tasks/version && npm run dist && git add -A dist js", "test": "npm run lint", "uglify": "npx uglifyjs dist/zdog.dist.js -o dist/zdog.dist.min.js --mangle --comments /^!/"}, "repository": {"type": "git", "url": "git+https://github.com/metafizzy/zdog.git"}, "keywords": ["3D", "canvas", "svg"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/metafizzy/zdog/issues"}, "homepage": "https://zzz.dog"}