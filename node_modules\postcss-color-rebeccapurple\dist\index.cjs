"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=e(require("postcss-value-parser"));const s=e=>{const s=Object.assign({preserve:!1},e);return{postcssPlugin:"postcss-color-rebeccapurple",Declaration(e){if(!e.value.toLowerCase().includes("rebeccapurple"))return;const t=r.default(e.value);t.walk((e=>{"word"===e.type&&"rebeccapurple"===e.value.toLowerCase()&&(e.value="#639")}));const o=String(t);o!==e.value&&(e.cloneBefore({value:o}),s.preserve||e.remove())}}};s.postcss=!0,module.exports=s;
