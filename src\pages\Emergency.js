import React, { useState, useEffect } from 'react';
import { 
  ExclamationTriangleIcon, 
  PhoneIcon, 
  MapPinIcon,
  UserIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useHealthStore } from '../store/healthStore';
import toast from 'react-hot-toast';

const Emergency = () => {
  const { user, setUser, setEmergencyMode } = useHealthStore();
  const [location, setLocation] = useState(null);
  const [nearbyHospitals, setNearbyHospitals] = useState([]);
  const [showAddContact, setShowAddContact] = useState(false);
  const [newContact, setNewContact] = useState({ name: '', phone: '', relationship: '' });

  const getCurrentLocation = React.useCallback(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const coords = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          };
          setLocation(coords);
          findNearbyHospitals(coords);
        },
        (error) => {
          console.error('Error getting location:', error);
          toast.error('Unable to get your location');
        }
      );
    }
  }, []);

  useEffect(() => {
    getCurrentLocation();
  }, [getCurrentLocation]);

  const findNearbyHospitals = (coords) => {
    // Simulated hospital data - in real app, use Google Places API or similar
    const mockHospitals = [
      {
        id: 1,
        name: 'City General Hospital',
        address: '123 Main St, City Center',
        phone: '******-0123',
        distance: '0.8 miles',
        emergency: true
      },
      {
        id: 2,
        name: 'St. Mary\'s Medical Center',
        address: '456 Oak Ave, Downtown',
        phone: '******-0456',
        distance: '1.2 miles',
        emergency: true
      },
      {
        id: 3,
        name: 'Regional Medical Clinic',
        address: '789 Pine St, Midtown',
        phone: '******-0789',
        distance: '2.1 miles',
        emergency: false
      }
    ];
    
    setNearbyHospitals(mockHospitals);
  };

  const triggerEmergencyAlert = () => {
    setEmergencyMode(true);
    
    // Simulate emergency alert
    toast.error('Emergency Alert Activated!', { duration: 5000 });
    
    // In real app, this would:
    // 1. Send SMS/calls to emergency contacts
    // 2. Share location with contacts
    // 3. Contact emergency services if configured
    // 4. Send health summary to first responders
    
    setTimeout(() => {
      setEmergencyMode(false);
    }, 30000); // Auto-disable after 30 seconds for demo
  };

  const addEmergencyContact = () => {
    if (!newContact.name || !newContact.phone) {
      toast.error('Please fill in name and phone number');
      return;
    }

    const contacts = user.emergencyContacts || [];
    const updatedContacts = [...contacts, { ...newContact, id: Date.now() }];
    
    setUser({ emergencyContacts: updatedContacts });
    setNewContact({ name: '', phone: '', relationship: '' });
    setShowAddContact(false);
    toast.success('Emergency contact added');
  };

  const removeEmergencyContact = (contactId) => {
    const contacts = user.emergencyContacts || [];
    const updatedContacts = contacts.filter(c => c.id !== contactId);
    setUser({ emergencyContacts: updatedContacts });
    toast.success('Emergency contact removed');
  };

  const callEmergencyServices = () => {
    // In real app, this would initiate a call
    window.open('tel:911');
  };

  const shareLocationWithContacts = () => {
    if (!location) {
      toast.error('Location not available');
      return;
    }

    // Simulate sharing location
    const locationUrl = `https://maps.google.com/?q=${location.latitude},${location.longitude}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Emergency Location Share',
        text: 'I need help. Here is my current location:',
        url: locationUrl
      });
    } else {
      // Fallback - copy to clipboard
      navigator.clipboard.writeText(`Emergency: I need help. My location: ${locationUrl}`);
      toast.success('Location copied to clipboard');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center space-x-3">
          <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
          <div>
            <h1 className="text-2xl font-bold text-red-900">Emergency Center</h1>
            <p className="text-red-700">Quick access to emergency services and contacts</p>
          </div>
        </div>
      </div>

      {/* Emergency Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={callEmergencyServices}
          className="bg-red-600 hover:bg-red-700 text-white p-6 rounded-lg flex flex-col items-center space-y-2 transition-colors"
        >
          <PhoneIcon className="h-8 w-8" />
          <span className="font-semibold">Call 911</span>
          <span className="text-sm opacity-90">Emergency Services</span>
        </button>
        
        <button
          onClick={triggerEmergencyAlert}
          className="bg-orange-600 hover:bg-orange-700 text-white p-6 rounded-lg flex flex-col items-center space-y-2 transition-colors"
        >
          <ExclamationTriangleIcon className="h-8 w-8" />
          <span className="font-semibold">Emergency Alert</span>
          <span className="text-sm opacity-90">Notify Contacts</span>
        </button>
        
        <button
          onClick={shareLocationWithContacts}
          className="bg-blue-600 hover:bg-blue-700 text-white p-6 rounded-lg flex flex-col items-center space-y-2 transition-colors"
        >
          <MapPinIcon className="h-8 w-8" />
          <span className="font-semibold">Share Location</span>
          <span className="text-sm opacity-90">Send to Contacts</span>
        </button>
      </div>

      {/* Emergency Contacts */}
      <div className="card">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Emergency Contacts</h3>
          <button
            onClick={() => setShowAddContact(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Contact</span>
          </button>
        </div>

        {showAddContact && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Add Emergency Contact</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <input
                type="text"
                placeholder="Full Name"
                value={newContact.name}
                onChange={(e) => setNewContact({...newContact, name: e.target.value})}
                className="input-field"
              />
              <input
                type="tel"
                placeholder="Phone Number"
                value={newContact.phone}
                onChange={(e) => setNewContact({...newContact, phone: e.target.value})}
                className="input-field"
              />
              <input
                type="text"
                placeholder="Relationship"
                value={newContact.relationship}
                onChange={(e) => setNewContact({...newContact, relationship: e.target.value})}
                className="input-field"
              />
            </div>
            <div className="flex justify-end space-x-3 mt-3">
              <button
                onClick={() => setShowAddContact(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={addEmergencyContact}
                className="btn-primary"
              >
                Add Contact
              </button>
            </div>
          </div>
        )}

        {user.emergencyContacts && user.emergencyContacts.length > 0 ? (
          <div className="space-y-3">
            {user.emergencyContacts.map((contact) => (
              <div key={contact.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <UserIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium text-gray-900">{contact.name}</div>
                    <div className="text-sm text-gray-500">
                      {contact.phone} • {contact.relationship}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => window.open(`tel:${contact.phone}`)}
                    className="text-green-600 hover:text-green-700"
                  >
                    <PhoneIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => removeEmergencyContact(contact.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <UserIcon className="mx-auto h-12 w-12 text-gray-300" />
            <p className="mt-2">No emergency contacts added</p>
            <button
              onClick={() => setShowAddContact(true)}
              className="mt-2 text-primary-600 hover:text-primary-500"
            >
              Add your first emergency contact
            </button>
          </div>
        )}
      </div>

      {/* Nearby Hospitals */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Nearby Medical Facilities</h3>
        {nearbyHospitals.length > 0 ? (
          <div className="space-y-3">
            {nearbyHospitals.map((hospital) => (
              <div key={hospital.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900 flex items-center space-x-2">
                    <span>{hospital.name}</span>
                    {hospital.emergency && (
                      <span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">
                        Emergency
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500">{hospital.address}</div>
                  <div className="text-sm text-gray-500">{hospital.distance} away</div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => window.open(`tel:${hospital.phone}`)}
                    className="text-green-600 hover:text-green-700"
                  >
                    <PhoneIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => window.open(`https://maps.google.com/?q=${encodeURIComponent(hospital.address)}`)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    <MapPinIcon className="h-5 w-5" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <MapPinIcon className="mx-auto h-12 w-12 text-gray-300" />
            <p className="mt-2">Unable to find nearby hospitals</p>
            <button
              onClick={getCurrentLocation}
              className="mt-2 text-primary-600 hover:text-primary-500"
            >
              Retry location detection
            </button>
          </div>
        )}
      </div>

      {/* Medical Information */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Medical Information</h3>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-sm text-yellow-800">
            <strong>Important:</strong> Keep your medical information up to date in your profile. 
            This information can be crucial for first responders and medical professionals.
          </p>
          <div className="mt-3">
            <button className="text-yellow-700 hover:text-yellow-600 font-medium">
              Update Medical Profile →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Emergency;