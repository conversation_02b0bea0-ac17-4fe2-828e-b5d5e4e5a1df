import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // Auth State
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null,

      // Actions
      login: async (email, password) => {
        set({ loading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Mock authentication - in real app, this would be an API call
          if (email && password) {
            const mockUser = {
              id: 1,
              email: email,
              name: email.split('@')[0],
              avatar: null,
              createdAt: new Date().toISOString()
            };
            
            const mockToken = 'mock-jwt-token-' + Date.now();
            
            set({
              isAuthenticated: true,
              user: mockUser,
              token: mockToken,
              loading: false,
              error: null
            });
            
            return { success: true, user: mockUser };
          } else {
            throw new Error('Invalid credentials');
          }
        } catch (error) {
          set({
            loading: false,
            error: error.message || 'Lo<PERSON> failed'
          });
          return { success: false, error: error.message };
        }
      },

      signup: async (name, email, password) => {
        set({ loading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1200));
          
          // Mock registration - in real app, this would be an API call
          if (name && email && password) {
            const mockUser = {
              id: Date.now(),
              email: email,
              name: name,
              avatar: null,
              createdAt: new Date().toISOString()
            };
            
            const mockToken = 'mock-jwt-token-' + Date.now();
            
            set({
              isAuthenticated: true,
              user: mockUser,
              token: mockToken,
              loading: false,
              error: null
            });
            
            return { success: true, user: mockUser };
          } else {
            throw new Error('All fields are required');
          }
        } catch (error) {
          set({
            loading: false,
            error: error.message || 'Registration failed'
          });
          return { success: false, error: error.message };
        }
      },

      logout: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          loading: false,
          error: null
        });
      },

      clearError: () => {
        set({ error: null });
      },

      // Google OAuth simulation
      loginWithGoogle: async () => {
        set({ loading: true, error: null });
        
        try {
          // Simulate Google OAuth
          await new Promise(resolve => setTimeout(resolve, 800));
          
          const mockUser = {
            id: Date.now(),
            email: '<EMAIL>',
            name: 'Google User',
            avatar: 'https://via.placeholder.com/40',
            provider: 'google',
            createdAt: new Date().toISOString()
          };
          
          const mockToken = 'mock-google-token-' + Date.now();
          
          set({
            isAuthenticated: true,
            user: mockUser,
            token: mockToken,
            loading: false,
            error: null
          });
          
          return { success: true, user: mockUser };
        } catch (error) {
          set({
            loading: false,
            error: 'Google authentication failed'
          });
          return { success: false, error: error.message };
        }
      },

      // Update user profile
      updateProfile: (updates) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates }
          });
        }
      }
    }),
    {
      name: 'medimate-auth-store',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token,
      }),
    }
  )
);