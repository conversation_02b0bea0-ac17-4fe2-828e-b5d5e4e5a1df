import e from"postcss-selector-parser";const t=e().astSync(":link").nodes[0],n=e().astSync(":visited").nodes[0],s=e().astSync("area[href]").nodes[0],o=e().astSync("[href]").nodes[0];function r(r,l){let c=[];return e((r=>{let a=[];r.walkPseudos((r=>{if(":any-link"!==r.value.toLowerCase()||r.nodes&&r.nodes.length)return;if(!l)return void a.push([t.clone(),n.clone()]);const c=function(t){let n=[],s=t.prev();for(;s&&"combinator"!==s.type&&!e.isPseudoElement(s);)"tag"===s.type&&n.push(s.value.toLowerCase()),s=s.prev();let o=t.next();for(;o&&"combinator"!==o.type&&!e.isPseudoElement(o);)"tag"===o.type&&n.push(o.value.toLowerCase()),o=o.next();return n}(r);c.includes("area")?a.push([t.clone(),n.clone(),o.clone()]):c.length?a.push([t.clone(),n.clone()]):a.push([t.clone(),n.clone(),s.clone()])})),a.length&&function(...e){const t=[],n=e.length-1;function s(o,r){for(let l=0,c=e[r].length;l<c;l++){const c=o.slice(0);c.push(e[r][l]),r==n?t.push(c):s(c,r+1)}}return s([],0),t}(...a).forEach((t=>{const n=r.clone();n.walkPseudos((n=>{":any-link"!==n.value.toLowerCase()||n.nodes&&n.nodes.length||(!function(t,n,s){let o=s.type;"selector"===s.type&&s.nodes&&s.nodes.length&&(o=s.nodes[0].type);let r=-1,l=-1;const c=t.index(n);for(let n=c;n>=0&&("combinator"!==t.nodes[n].type&&!e.isPseudoElement(t.nodes[n].type));n--)r=n;if("tag"===o)return void t.insertBefore(t.at(r),s);for(let n=c;n<t.nodes.length&&("combinator"!==t.nodes[n].type&&!e.isPseudoElement(t.nodes[n].type));n++)l=n;for(let e=r;e<=l;e++)if(t.nodes[e].type===o)return void t.insertAfter(t.at(e),s);t.insertAfter(t.at(r),s)}(n.parent,n,t.shift()),n.remove())})),c.push(n.toString())}))})).processSync(r),c}function l(e){const t={preserve:!0,...e},n={areaHrefNeedsFixing:!1,...Object(t.subFeatures)};return{postcssPlugin:"postcss-pseudo-class-any-link",Rule(e,{result:s}){if(!e.selector.toLowerCase().includes(":any-link"))return;(e.raws.selector&&e.raws.selector.raw||e.selector).endsWith(":")||function(e,t,n,s){let o=[],l=[];try{for(let t=0;t<e.selectors.length;t++){const n=e.selectors[t],c=r(n,s);c.length?o.push(...c):l.push(n)}}catch(n){return void e.warn(t,`Failed to parse selector : ${e.selector}`)}o.length&&(e.cloneBefore({selectors:o}),l.length&&e.cloneBefore({selectors:l}),n||e.remove())}(e,s,t.preserve,n.areaHrefNeedsFixing)}}}l.postcss=!0;export{l as default};
